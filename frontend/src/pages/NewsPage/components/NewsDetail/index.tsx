import React, { useEffect } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { Calendar, User, Globe, Languages } from 'lucide-react';
import { motion } from 'framer-motion';
import { Header } from '../../../../components/layout/Header';
import { Background } from '../../../../components/Background';
import { NewsContent } from './NewsContent';
import { RelatedNews } from './RelatedNews';
import { useNewsDetail } from '../../hooks/useNewsDetail';
import { useRelatedNews } from '../../hooks/useRelatedNews';
import { useNewsReadStatus } from '../../hooks/useNewsReadStatus';

export function NewsDetail() {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const fromType = searchParams.get('from') || 'latest';
  const { news, isLoading, showOriginal, toggleOriginal } = useNewsDetail(id!);
  const { relatedNews } = useRelatedNews(id!, news?.tags || []);
  const { markAsRead, readNewsIds } = useNewsReadStatus();

  // 当新闻加载完成时，标记为已读
  useEffect(() => {
    if (news && id && !readNewsIds.includes(id)) {
      markAsRead(id);
    }
  }, [news, id, markAsRead, readNewsIds]);

  if (isLoading) {
    return (
      <div className="min-h-screen">
        <Background />
        <Header />
        <main className="relative z-10 pt-24">
          <div className="flex justify-center py-12">
            <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
          </div>
        </main>
      </div>
    );
  }

  if (!news) {
    return (
      <div className="min-h-screen">
        <Background />
        <Header />
        <main className="relative z-10 pt-24">
          <div className="text-center py-12 text-gray-400">
            未找到相关新闻
          </div>
        </main>
      </div>
    );
  }

  // 获取作者名称
  const authorName = typeof news.author === 'string' ? news.author : news.author?.name || '未知作者';

  return (
    <div className="min-h-screen">
      <Background />
      <Header />

      <main className="relative z-10 pt-24 pb-12">
        <motion.article
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-4xl lg:max-w-5xl 2xl:max-w-6xl mx-auto px-6 lg:px-8"
        >
          {/* Article Header */}
          <header className="mb-8">
            <div className="flex justify-between items-start mb-4">
              <h1 className="text-2xl lg:text-3xl font-extrabold text-white leading-snug">
                {showOriginal ? (news.title_en || news.title) : (news.title_cn || news.title)}
              </h1>

              {/* 当有任何中文内容（标题或摘要）时显示翻译按钮 */}
              {(news.title_cn || news.summary_cn || news.content_cn) && (
                <button
                  onClick={toggleOriginal}
                  className="flex items-center gap-2 px-3 py-2 bg-blue-500/20 hover:bg-blue-500/30 rounded-lg text-blue-400 transition-colors"
                >
                  <Languages className="w-4 h-4" />
                  <span>{showOriginal ? '查看中文' : '查看原文'}</span>
                </button>
              )}
            </div>

            <div className="flex items-center gap-6 text-base lg:text-lg text-gray-400">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>{news.date}</span>
              </div>

              <div className="flex items-center gap-2">
                <User className="w-4 h-4" />
                <span>{authorName}</span>
              </div>

              {news.source && (
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4" />
                  <span>{news.source}</span>
                </div>
              )}
            </div>
          </header>

          {/* Featured Image */}
          {news.imageUrl && (
            <div className="mb-8 rounded-xl overflow-hidden">
              <img
                src={news.imageUrl}
                alt={news.title}
                className="w-full h-[480px] object-cover"
              />
            </div>
          )}

          {/* Summary */}
          <section className="mb-12">
            <h3 className="text-2xl lg:text-3xl font-bold text-white mb-4 border-l-4 border-blue-500 pl-3">
              摘要
            </h3>
            <p className="text-sm lg:text-base text-gray-200/90 leading-relaxed indent-8">
              {showOriginal ? (news.summary_en || news.summary) : (news.summary_cn || news.summary)}
            </p>
          </section>

          {/* Article Content */}
          <section className="mb-12">
            <h2 className="text-2xl lg:text-3xl font-bold text-white mb-6 border-l-4 border-purple-500 pl-3">
              正文
            </h2>
            <div className="text-lg lg:text-xl xl:text-2xl">
              <NewsContent 
                content={news.content || ''} 
                content_cn={news.content_cn}
                showOriginal={showOriginal}
              />
            </div>
          </section>

          {/* Tags */}
          <div className="flex flex-wrap gap-2">
            {Array.isArray(news.tags) && news.tags.length > 0 && news.tags.map(tag => (
              <span
                key={tag}
                className="px-3 py-1.5 rounded-full text-sm bg-white/5 text-gray-300"
              >
                {tag}
              </span>
            ))}
          </div>

          {/* 原文链接 */}
          {news.originalData?.info_source && (
            <div className="mt-8">
              <a
                href={news.originalData.info_source}
                target="_blank"
                rel="noopener noreferrer"
                className="group relative inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-500 hover:to-purple-500 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-out overflow-hidden"
              >
                {/* 背景光效 */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                
                {/* 图标 */}
                <div className="relative z-10 p-1 bg-white/10 rounded-lg group-hover:bg-white/20 transition-colors duration-300">
                  <Globe className="w-4 h-4" />
                </div>
                
                {/* 文字 */}
                <span className="relative z-10 tracking-wide">原文链接</span>
                
                {/* 右侧箭头 */}
                <div className="relative z-10 w-5 h-5 flex items-center justify-center">
                  <svg 
                    className="w-4 h-4 transform group-hover:translate-x-1 transition-transform duration-300" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </div>
              </a>
            </div>
          )}

          {/* Related News */}
          {relatedNews.length > 0 && (
            <RelatedNews news={relatedNews} />
          )}
        </motion.article>
      </main>
    </div>
  );
}