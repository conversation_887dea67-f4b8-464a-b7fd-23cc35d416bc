import React, { useState, useEffect } from 'react';
import { parseContent, parseBilingualContent, isEntityDataAvailable } from '../../../../utils/contentParser';

interface NewsContentProps {
  content?: string;
  showOriginal?: boolean;
  content_cn?: string;
}

/**
 * 简单的HTML清理函数，作为实体解析失败时的备用方案
 */
function sanitizeContent(content: string): string {
  if (!content) return '';
  
  // 基本的HTML清理和格式化
  return content
    .replace(/\n\n/g, '</p><p>')
    .replace(/\n/g, '<br />')
    .replace(/^(.*)/, '<p>$1')
    .replace(/(.*$)/, '$1</p>')
    .replace(/<p><\/p>/g, '');
}

export function NewsContent({ content = '', showOriginal = false, content_cn }: NewsContentProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [entityDataReady, setEntityDataReady] = useState(false);

  useEffect(() => {
    // 异步检查实体数据状态，避免阻塞渲染
    const checkEntityData = async () => {
      try {
        const dataAvailable = isEntityDataAvailable();
        setEntityDataReady(dataAvailable);
      } catch (error) {
        console.warn('检查实体数据状态失败:', error);
        setEntityDataReady(false);
      } finally {
        setIsLoading(false);
      }
    };

    // 延迟执行，避免阻塞初始渲染
    const timer = setTimeout(checkEntityData, 50);
    return () => clearTimeout(timer);
  }, [content, content_cn]);

  const processedContent = React.useMemo(() => {
    // 如果没有内容，返回默认文本
    if (!content && !content_cn) {
      return '<p>暂无内容</p>';
    }

    // 选择要显示的内容
    const displayContent = showOriginal ? (content || '') : (content_cn || content || '');

    // 如果实体数据未准备好，使用简单处理避免错误
    if (!entityDataReady) {
      return sanitizeContent(displayContent);
    }

    try {
      // 如果只有一种语言的内容，直接解析
      if (!content_cn || !content) {
        return parseContent(displayContent);
      }

      // 如果有双语内容，使用双语解析确保一致性
      const bilingualResult = parseBilingualContent(content, content_cn);
      return showOriginal ? bilingualResult.original : bilingualResult.translated;
    } catch (error) {
      console.warn('实体解析失败，使用简单处理:', error);
      return sanitizeContent(displayContent);
    }

  }, [content, content_cn, showOriginal, entityDataReady]);

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
          <span className="text-white/70 text-sm">加载内容中...</span>
        </div>
      </div>
    );
  }

  return (
    <div
      className="prose prose-invert prose-lg lg:prose-3xl xl:prose-4xl leading-10 max-w-none text-gray-200 [&>p]:text-xl lg:[&>p]:text-2xl xl:[&>p]:text-3xl"
      dangerouslySetInnerHTML={{ __html: processedContent }}
    />
  );
}