import React from 'react';
import styled from 'styled-components';
import { WikiLaunchSiteInfo } from '../../../types/launchSite';

const Panel = styled.div`
  position: fixed;
  top: 40%;
  right: 20px;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  padding: 24px;
  border-radius: 16px;
  width: 360px;
  max-height: 52vh;
  overflow-y: auto;
  color: white;
  z-index: 1000;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
`;

const TitleBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 12px;
`;

const Title = styled.h3`
  margin: 0;
  font-size: 20px;
  color: #fff;
  font-weight: 600;
  letter-spacing: 0.5px;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
  }
`;

const InfoSection = styled.div`
  margin-bottom: 20px;
  
  &:last-of-type {
    margin-bottom: 16px;
  }
`;

const SectionTitle = styled.h4`
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #00bcd4;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  border-left: 3px solid #00bcd4;
  padding-left: 8px;
`;

const InfoItem = styled.div`
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
  padding: 6px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  
  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }
`;

const Label = styled.span`
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  margin-right: 12px;
  min-width: 85px;
  flex-shrink: 0;
  font-weight: 500;
`;

const Value = styled.div`
  color: rgba(255, 255, 255, 0.95);
  font-size: 13px;
  flex: 1;
  word-break: break-word;
  line-height: 1.4;
  font-weight: 400;
`;

const Description = styled.div`
  color: rgba(255, 255, 255, 0.95);
  font-size: 13px;
  line-height: 1.4;
  font-weight: 400;
  word-break: break-word;
`;

const DetailsButton = styled.button`
  width: 100%;
  background: linear-gradient(135deg, #00bcd4, #0097a7);
  border: none;
  border-radius: 8px;
  color: white;
  padding: 14px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 600;
  margin-top: 20px;
  letter-spacing: 0.5px;
  text-transform: uppercase;

  &:hover {
    background: linear-gradient(135deg, #0097a7, #00838f);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 188, 212, 0.4);
  }

  &:active {
    transform: translateY(0);
  }

  svg {
    width: 18px;
    height: 18px;
  }
`;

const LinkButton = styled.button`
  background: none;
  border: none;
  color: #4a9eff;
  text-decoration: underline;
  cursor: pointer;
  font-size: 12px;
  padding: 0;
  margin-top: 4px;

  &:hover {
    color: #66b3ff;
  }
`;

interface LaunchSiteWikiPanelProps {
  siteName: string;
  wikiInfo: WikiLaunchSiteInfo;
  onClose: () => void;
}

export const LaunchSiteWikiPanel: React.FC<LaunchSiteWikiPanelProps> = ({
  siteName,
  wikiInfo,
  onClose,
}) => {
  const handleViewDetails = () => {
    // 构建URL，如果有_id则作为查询参数传递
    let url = `/launch-site/${encodeURIComponent(siteName)}`;
    if (wikiInfo._id) {
      url += `?_id=${encodeURIComponent(wikiInfo._id)}`;
    }
    window.open(url, '_blank');
  };

  const handleOpenWikiSource = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <Panel>
      <TitleBar>
        <Title>{wikiInfo.site_name?.[0] || siteName}</Title>
        <CloseButton onClick={onClose} title="关闭">
          ×
        </CloseButton>
      </TitleBar>

      {/* 基本信息 */}
      <InfoSection>
        <SectionTitle>基本信息</SectionTitle>
        
        <InfoItem>
          <Label>国家:</Label>
          <Value>{wikiInfo.country}</Value>
        </InfoItem>
        
        <InfoItem>
          <Label>位置:</Label>
          <Value>{wikiInfo.location}</Value>
        </InfoItem>
        
        <InfoItem>
          <Label>运营时间:</Label>
          <Value>{wikiInfo.operational_date}</Value>
        </InfoItem>
      </InfoSection>

      {/* 发射统计 */}
      <InfoSection>
        <SectionTitle>发射统计</SectionTitle>
        
        <InfoItem>
          <Label>发射次数:</Label>
          <Value>{wikiInfo.launch_num}</Value>
        </InfoItem>
        
        <InfoItem>
          <Label>最重载荷:</Label>
          <Value>{wikiInfo.launch_heaviest}</Value>
        </InfoItem>
        
        <InfoItem>
          <Label>最高轨道:</Label>
          <Value>{wikiInfo.launch_highest}</Value>
        </InfoItem>
      </InfoSection>

      {/* 详细说明 */}
      {wikiInfo.notes && (
        <InfoSection>
          <SectionTitle>详细说明</SectionTitle>
          <InfoItem>
            <Description>{wikiInfo.notes}</Description>
          </InfoItem>
        </InfoSection>
      )}



      <DetailsButton onClick={handleViewDetails} title="查看详细信息">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
        查看详情
      </DetailsButton>
    </Panel>
  );
};
