import React from 'react';
import styled from 'styled-components';
import { Satellite } from '../../../types/satellite';

const Panel = styled.div<{ visible: boolean }>`
  position: fixed;
  right: 20px;
  top: 45%;
  transform: translateY(-50%);
  width: 320px;
  max-height: 46.67vh; /* 原来是70vh，改为2/3，即70 * 2/3 ≈ 46.67vh */
  background: transparent;  // 完全透明
  border-radius: 8px;
  padding: 20px;
  color: white;
  backdrop-filter: blur(8px);  // 保留模糊效果以提高文字可读性
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  opacity: ${props => props.visible ? 1 : 0};
  visibility: ${props => props.visible ? 'visible' : 'hidden'};
  z-index: 1000;
  pointer-events: ${props => props.visible ? 'auto' : 'none'};
  overflow-y: auto;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.05);  // 添加微弱的高光效果
    border-radius: 8px;
    pointer-events: none;
  }

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
`;

const TitleBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 8px;
`;

const Title = styled.h3`
  margin: 0;
  font-size: 18px;
  color: #fff;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  font-size: 20px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s;

  &:hover {
    color: #fff;
  }
`;

const InfoItem = styled.div`
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
`;

const Label = styled.span`
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin-right: 8px;
  min-width: 80px;
`;

const Value = styled.div`
  color: #fff;
  font-size: 14px;
  flex: 1;
`;

const PurposeText = styled.div`
  margin-bottom: 4px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const DetailsButton = styled.button`
  position: absolute;
  right: 20px;
  bottom: 20px;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.7);
  padding: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
  }

  svg {
    width: 16px;
    height: 16px;
  }
`;

interface SatelliteInfoPanelProps {
  satellite: Satellite;
  onClose: () => void;
  onViewDetails?: () => void;
}

// 通信类型翻译映射
const communicationTypeMap: { [key: string]: string } = {
  'Communications': '通信',
  'Internet Service': '互联网服务',
  'Navigation': '导航',
  'Positioning': '定位',
  'Remote Sensing': '遥感',
};

export const SatelliteInfoPanel: React.FC<SatelliteInfoPanelProps> = ({
  satellite,
  onClose,
  onViewDetails,
}) => {
  if (!satellite) return null;

  const handleViewDetails = (id: string) => {
    const url = `/satellite/${id}`;
    window.open(url, '_blank');
  };

  // 处理卫星用途的显示
  const renderPurpose = () => {
    if (typeof satellite.purpose === 'string') {
      return <PurposeText>{communicationTypeMap[satellite.purpose] || satellite.purpose}</PurposeText>;
    }
    return (
      <>
        <PurposeText>{communicationTypeMap[satellite.purpose.primary] || satellite.purpose.primary}</PurposeText>
        {satellite.purpose.detailed && (
          <PurposeText style={{ color: 'rgba(255, 255, 255, 0.7)', fontSize: '12px' }}>
            {communicationTypeMap[satellite.purpose.detailed] || satellite.purpose.detailed}
          </PurposeText>
        )}
      </>
    );
  };

  return (
    <Panel visible={true}>
      <TitleBar>
        <Title>{satellite.name}</Title>
        {onClose && (
          <CloseButton onClick={onClose} title="关闭">
            ×
          </CloseButton>
        )}
      </TitleBar>
      <InfoItem>
        <Label>星座系统</Label>
        <Value>{satellite.constellationName}</Value>
      </InfoItem>
      <InfoItem>
        <Label>运营商</Label>
        <Value>{satellite.operator}</Value>
      </InfoItem>
      <InfoItem>
        <Label>用途</Label>
        <Value>{renderPurpose()}</Value>
      </InfoItem>
      <InfoItem>
        <Label>轨道类型</Label>
        <Value>{satellite.configuration}</Value>
      </InfoItem>
      <InfoItem>
        <Label>轨道倾角</Label>
        <Value>{satellite.orbitInfo.inclination.toFixed(1)}°</Value>
      </InfoItem>
      <InfoItem>
        <Label>平均运动</Label>
        <Value>{satellite.orbitInfo.meanMotion.toFixed(2)} 圈/天</Value>
      </InfoItem>
      <InfoItem>
        <Label>偏心率</Label>
        <Value>{satellite.orbitInfo.eccentricity.toFixed(4)}</Value>
      </InfoItem>
      <InfoItem>
        <Label>发射日期</Label>
        <Value>{new Date(satellite.launchInfo.launchDate).toLocaleDateString('zh-CN')}</Value>
      </InfoItem>
      <DetailsButton onClick={() => handleViewDetails(satellite.id)} title="查看详细信息">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
      </DetailsButton>
    </Panel>
  );
}; 