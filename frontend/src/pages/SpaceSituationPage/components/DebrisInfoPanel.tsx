import React from 'react';
import styled from 'styled-components';
import { SpaceDebris } from '../../../types/debris';

const Panel = styled.div`
  position: fixed;
  top: 45%;
  right: 20px;
  transform: translateY(-50%);
  background: transparent;
  backdrop-filter: blur(8px);
  padding: 20px;
  border-radius: 8px;
  width: 300px;
  color: white;
  z-index: 1000;
  max-height: 70vh;
  overflow-y: auto;
`;

const TitleBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 8px;
`;

const Title = styled.h3`
  margin: 0;
  font-size: 18px;
  font-weight: 500;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  font-size: 20px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s;

  &:hover {
    color: #fff;
  }
`;

const InfoItem = styled.div`
  margin-bottom: 12px;
`;

const Label = styled.span`
  color: rgba(255, 255, 255, 0.6);
  margin-right: 8px;
`;

const Value = styled.span`
  color: rgba(255, 255, 255, 0.9);
`;

const DetailsButton = styled.button`
  position: absolute;
  bottom: 15px;
  right: 15px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }

  svg {
    width: 16px;
    height: 16px;
    fill: white;
  }
`;

interface DebrisInfoPanelProps {
  debris: SpaceDebris;
  onClose?: () => void;
  onViewDetails?: () => void;
}

export const DebrisInfoPanel: React.FC<DebrisInfoPanelProps> = ({
  debris,
  onClose,
  onViewDetails,
}) => {
  const handleViewDetails = (id: string) => {
    const url = `/debris/${id}`;
    window.open(url, '_blank');
  };

  return (
    <Panel>
      <TitleBar>
        <Title>{debris.name}</Title>
        {onClose && (
          <CloseButton onClick={onClose} title="关闭">
            ×
          </CloseButton>
        )}
      </TitleBar>
      <InfoItem>
        <Label>类型：</Label>
        <Value>{debris.type}</Value>
      </InfoItem>
      <InfoItem>
        <Label>状态：</Label>
        <Value>{debris.status}</Value>
      </InfoItem>
      <InfoItem>
        <Label>来源：</Label>
        <Value>{debris.source}</Value>
      </InfoItem>
      <InfoItem>
        <Label>产生时间：</Label>
        <Value>{debris.generationTime}</Value>
      </InfoItem>
      <InfoItem>
        <Label>尺寸：</Label>
        <Value>{debris.size}</Value>
      </InfoItem>
      <InfoItem>
        <Label>质量：</Label>
        <Value>{debris.mass}</Value>
      </InfoItem>
      <DetailsButton onClick={() => handleViewDetails(debris.id)} title="查看详情">
        <svg viewBox="0 0 24 24">
          <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z" />
        </svg>
      </DetailsButton>
    </Panel>
  );
}; 