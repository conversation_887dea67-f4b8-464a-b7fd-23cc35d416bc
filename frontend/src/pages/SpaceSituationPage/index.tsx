import React, { useCallback, useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { Header } from '../../components/layout/Header';
import { CesiumViewer } from './components/CesiumViewer';
import { CesiumController } from './controllers/CesiumController';
import { TimeController } from './components/TimeController';
import { SideButtons } from './components/SideButtons';
import { LayersPanel } from './components/LayersPanel';
import { SatelliteInfoPanel } from './components/SatelliteInfoPanel';
import { SatelliteBasicInfoPanel, SatelliteApiData } from './components/SatelliteBasicInfoPanel';
import { DebrisInfoPanel } from './components/DebrisInfoPanel';
import { LaunchSiteInfoPanel } from './components/LaunchSiteInfoPanel';
import { LaunchSiteWikiPanel } from './components/LaunchSiteWikiPanel';
import { Satellite } from '../../types/satellite';
import type { SpaceDebris } from '../../types/debris';
import { LaunchSite, WikiLaunchSiteInfo } from '../../types/launchSite';
import { useSearchParams } from 'react-router-dom';
import { apiService } from '../../services/apiService';
import { Modal, message } from 'antd';
import { constellationDataManager } from '../../services/constellationDataManager';

// 定义API响应的接口
interface ApiResponse<T> {
  success?: boolean;
  data?: T;
  results?: T[];
  total?: number;
  page?: number;
  limit?: number;
}

const PageContainer = styled.div`
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #000;
  position: relative;
  overflow: hidden;
`;

const MainContent = styled.div`
  flex: 1;
  position: relative;
  
  .cesium-container {
    width: 100%;
    height: 100%;
  }
`;

export function SpaceSituationPage() {
  const [layersPanelVisible, setLayersPanelVisible] = useState(true);
  const [cesiumController, setCesiumController] = useState<CesiumController | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isPlaying, setIsPlaying] = useState(true);
  const [multiplier, setMultiplier] = useState(1);
  const [currentTime, setCurrentTime] = useState(0);
  const [selectedSatellite, setSelectedSatellite] = useState<Satellite | null>(null);
  const [selectedDebris, setSelectedDebris] = useState<SpaceDebris | null>(null);
  const [selectedLaunchSite, setSelectedLaunchSite] = useState<LaunchSite | null>(null);
  const [isTimelineDisabled, setIsTimelineDisabled] = useState(false);
  const [searchParams] = useSearchParams();

  // 发射场Wiki面板状态
  const [launchSiteWikiInfo, setLaunchSiteWikiInfo] = useState<{siteName: string, wikiInfo: WikiLaunchSiteInfo} | null>(null);
  const [launchSiteWikiVisible, setLaunchSiteWikiVisible] = useState(false);

  // 🌟 新增：卫星基础信息面板状态
  const [satelliteBasicInfo, setSatelliteBasicInfo] = useState<SatelliteApiData | null>(null);
  const [satelliteBasicInfoLoading, setSatelliteBasicInfoLoading] = useState(false);
  const [satelliteBasicInfoError, setSatelliteBasicInfoError] = useState<string | null>(null);
  const [satelliteBasicInfoVisible, setSatelliteBasicInfoVisible] = useState(false);
  const [currentRequestedSatelliteId, setCurrentRequestedSatelliteId] = useState<string | null>(null);
  
  // 🌟 新增：防止竞态条件的标志
  const [isSatelliteClickInProgress, setIsSatelliteClickInProgress] = useState(false);

  const handleControllerReady = useCallback((cesiumController: CesiumController) => {
    setCesiumController(cesiumController);
  }, []);

  const checkSatelliteCount = useCallback(() => {
    if (cesiumController) {
      const isLimitedMode = cesiumController.checkSatelliteCountAndSetMode();
      setIsTimelineDisabled(isLimitedMode);
    }
  }, [cesiumController]);

  const handlePlayPauseToggle = useCallback(() => {
    if (cesiumController) {
      if (cesiumController.isInLimitedTrajectoryMode()) {
        Modal.info({
          title: '功能暂时不可用',
          content: '卫星实体数量大于500颗，为保证页面顺畅时间刻度条功能关闭',
          okText: '我知道了',
          maskClosable: true,
        });
        return;
      }

      if (cesiumController.isClockAnimating()) {
        cesiumController.stopClock();
        setIsPlaying(false);
      } else {
        cesiumController.startClock();
        setIsPlaying(true);
      }
    }
  }, [cesiumController]);

  const handleMultiplierChange = (multiplier: number) => {
    if (cesiumController) {
      if (cesiumController.isInLimitedTrajectoryMode()) {
        Modal.info({
          title: '功能暂时不可用',
          content: '卫星实体数量大于500颗，为保证页面顺畅时间刻度条功能关闭',
          okText: '我知道了',
          maskClosable: true,
        });
        return;
      }

      cesiumController.setTimeMultiplier(multiplier);
      setMultiplier(multiplier);
    }
  };

  const handleRealTimeClick = useCallback(() => {
    if (cesiumController) {
      if (cesiumController.isInLimitedTrajectoryMode()) {
        Modal.info({
          title: '功能暂时不可用',
          content: '卫星实体数量大于500颗，为保证页面顺畅时间刻度条功能关闭',
          okText: '我知道了',
          maskClosable: true,
        });
        return;
      }

      const currentSystemTime = new Date();
      cesiumController.setCurrentTime(currentSystemTime);
      
      cesiumController.syncAllSatellitesToCurrentTime();
      
      console.log('已将所有卫星同步到当前系统时间:', currentSystemTime.toISOString());
    }
  }, [cesiumController]);

  useEffect(() => {
    if (cesiumController) {
      cesiumController.startClock();
      setIsPlaying(true);
      
      checkSatelliteCount();
    }
  }, [cesiumController, checkSatelliteCount]);

  const handleSatelliteClick = useCallback((satellite: Satellite) => {
    console.log('🛰️ [第一优先级] 卫星点击处理:', satellite.name || satellite.id);
    
    if (!cesiumController || !satellite.id) {
      console.warn('⚠️ 卫星点击被忽略: 缺少控制器或卫星ID');
      return;
    }
    
    setSelectedSatellite(satellite);
    console.log('✅ [第一优先级] 卫星已选中，应显示信息面板');
    
    if (cesiumController.isInLimitedTrajectoryMode()) {
      cesiumController.selectSatelliteForDetailedTrajectory(satellite.id);
    }
  }, [cesiumController]);

  const handleClearSelectedSatellite = useCallback(() => {
    if (!cesiumController) return;
    
    setSelectedSatellite(null);
    
    if (cesiumController.isInLimitedTrajectoryMode()) {
      cesiumController.clearSelectedSatellite();
    }
  }, [cesiumController]);

  const handleLayersClick = () => {
    setLayersPanelVisible(!layersPanelVisible);
    console.log('Layers clicked');
  };

  // 🌟 新增：处理卫星信息请求
  const handleSatelliteInfoRequest = useCallback(async (satelliteId: string, noradId: number) => {
    console.log(`📋 [第二优先级] 卫星信息请求: ${satelliteId} (NORAD: ${noradId})`);
    
    // 🌟 设置卫星点击处理标志，防止document点击事件误关闭面板
    setIsSatelliteClickInProgress(true);
    console.log('🚀 设置卫星点击处理标志: isSatelliteClickInProgress = true');
    
    // 🌟 重要：每次请求新卫星信息时，先清空之前的数据
    setCurrentRequestedSatelliteId(satelliteId);
    setSatelliteBasicInfo(null);
    setSatelliteBasicInfoLoading(true);
    setSatelliteBasicInfoError(null);
    setSatelliteBasicInfoVisible(true);
    
    console.log('📋 [第二优先级] 基础信息面板状态设置: visible=true, loading=true');
    
    console.log('📋 设置面板状态: loading=true, visible=true, 清空之前数据');
    
    try {
      console.log('📋 准备调用API...');
      console.log('📋 API端点: /api/v1/database/filter-satellites');
      console.log('📋 请求参数:', { noradId: noradId, page: 1, limit: 10 });
      
      // 调用API获取卫星详细信息
      const response = await apiService.post<{
        success: boolean;
        results: SatelliteApiData[];
        total: number;
      }>('/api/v1/database/filter-satellites', {
        noradId: noradId,
        page: 1,
        limit: 10
      });
      
      console.log('📋 API响应:', response);
      
      if (response.success && response.results && response.results.length > 0) {
        setSatelliteBasicInfo(response.results[0]);
        console.log('📋 成功获取卫星信息:', response.results[0]);
        console.log('📋 设置面板数据完成');
      } else {
        // 🌟 如果API没有返回数据，创建一个基本的卫星信息对象
        const basicSatelliteInfo: SatelliteApiData = {
          id: Date.now(),
          satellite_name: [{ value: satelliteId, sources: ['local'] }],
          alternative_name: [],
          cospar_id: [],
          country_of_registry: [],
          owner: [],
          status: [{ value: '未知', sources: ['local'] }],
          norad_id: [{ value: noradId, sources: ['local'] }],
          launch_info: [],
          orbit_info: [],
          purpose: [],
          constellation: [],
          users: []
        };
        setSatelliteBasicInfo(basicSatelliteInfo);
        console.warn('📋 未找到卫星详细信息，使用基本信息，响应:', response);
      }
    } catch (error) {
      console.error('📋 获取卫星信息失败:', error);
      console.error('📋 错误详情:', {
        message: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined,
        response: (error as any)?.response?.data
      });
      
      // 🌟 即使API失败，也显示基本的卫星信息
      const basicSatelliteInfo: SatelliteApiData = {
        id: Date.now(),
        satellite_name: [{ value: satelliteId, sources: ['local'] }],
        alternative_name: [],
        cospar_id: [],
        country_of_registry: [],
        owner: [],
        status: [{ value: 'API连接失败', sources: ['local'] }],
        norad_id: [{ value: noradId, sources: ['local'] }],
        launch_info: [],
        orbit_info: [],
        purpose: [],
        constellation: [],
        users: []
      };
      setSatelliteBasicInfo(basicSatelliteInfo);
      setSatelliteBasicInfoError('网络连接失败，显示基本信息');
    } finally {
      setSatelliteBasicInfoLoading(false);
      console.log('📋 设置面板状态: loading=false');
      
      // 🌟 延迟清除卫星点击处理标志，确保document点击事件不会误触发关闭
      setTimeout(() => {
        setIsSatelliteClickInProgress(false);
        console.log('🏁 清除卫星点击处理标志: isSatelliteClickInProgress = false');
      }, 600); // 增加到600ms，确保足够的安全间隔
    }
  }, []);

  // 🌟 新增：关闭卫星基础信息面板
  const handleCloseSatelliteBasicInfo = useCallback(() => {
    setSatelliteBasicInfoVisible(false);
    setSatelliteBasicInfo(null);
    setSatelliteBasicInfoError(null);
    setCurrentRequestedSatelliteId(null);
    // 🌟 清除点击处理标志
    setIsSatelliteClickInProgress(false);
    console.log('📋 手动关闭卫星基础信息面板，清除点击处理标志');
  }, []);

  // 发射场点击处理
  const handleLaunchSiteClick = useCallback((data: any) => {
    console.log('🚀 收到发射场点击事件:', data);

    if (data.type === 'wiki' && data.siteName && data.wikiInfo) {
      // 显示Wiki信息面板
      setLaunchSiteWikiInfo({ siteName: data.siteName, wikiInfo: data.wikiInfo });
      setLaunchSiteWikiVisible(true);
      // 隐藏基本信息面板
      setSelectedLaunchSite(null);
    } else if (data.type === 'basic' && data.site) {
      // 显示基本信息面板
      setSelectedLaunchSite(data.site);
      // 隐藏Wiki面板
      setLaunchSiteWikiVisible(false);
      setLaunchSiteWikiInfo(null);
    }
  }, []);

  // 关闭发射场Wiki面板
  const handleCloseLaunchSiteWiki = useCallback(() => {
    setLaunchSiteWikiVisible(false);
    setLaunchSiteWikiInfo(null);
  }, []);

  // 🌟 新增：关闭所有信息面板的统一函数
  const handleCloseAllInfoPanels = useCallback(() => {
    // 🌟 清除卫星点击处理标志
    setIsSatelliteClickInProgress(false);
    console.log('🔄 关闭所有面板，清除卫星点击处理标志');
    
    // 关闭所有信息面板
    if (selectedSatellite) {
      handleClearSelectedSatellite();
    }
    if (selectedDebris) {
      setSelectedDebris(null);
    }
    if (selectedLaunchSite) {
      setSelectedLaunchSite(null);
    }
    if (launchSiteWikiVisible) {
      handleCloseLaunchSiteWiki();
    }
    if (satelliteBasicInfoVisible) {
      handleCloseSatelliteBasicInfo();
    }
  }, [
    selectedSatellite, 
    selectedDebris, 
    selectedLaunchSite, 
    launchSiteWikiVisible, 
    satelliteBasicInfoVisible,
    handleClearSelectedSatellite,
    handleCloseLaunchSiteWiki,
    handleCloseSatelliteBasicInfo
  ]);

  // 🌟 新增：处理键盘事件（ESC键关闭面板）
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      // 检查是否有任何面板打开
      const hasOpenPanels = selectedSatellite || 
                           selectedDebris || 
                           selectedLaunchSite || 
                           launchSiteWikiVisible || 
                           satelliteBasicInfoVisible;
      
      if (hasOpenPanels) {
        console.log('按下ESC键，关闭所有信息面板');
        handleCloseAllInfoPanels();
      }
    }
  }, [
    selectedSatellite,
    selectedDebris,
    selectedLaunchSite,
    launchSiteWikiVisible,
    satelliteBasicInfoVisible,
    handleCloseAllInfoPanels
  ]);

  // 🌟 新增：处理文档点击事件（点击面板外部关闭面板）
  const handleDocumentClick = useCallback((event: MouseEvent) => {
    const target = event.target as HTMLElement;
    
    // 🌟 移除canvas检测，因为过于宽泛
    // const isCesiumCanvasClick = target.tagName.toLowerCase() === 'canvas' && 
    //                            (target.closest('.cesium-viewer') || target.closest('.cesium-container'));
    
    // 🌟 只保护必要的UI控件，不保护信息面板本身
    const isClickInsideProtectedArea = 
      // 图层面板（需要保护，因为它是功能面板）
      target.closest('[class*="LayersPanel"]') ||
      // Ant Design 系统组件
      target.closest('.ant-modal') ||
      target.closest('.ant-drawer') ||
      target.closest('.ant-popover') ||
      target.closest('.ant-tooltip') ||
      target.closest('.ant-message') ||
      target.closest('.ant-notification') ||
      // 侧边控制按钮
      target.closest('[class*="SideButtons"]') ||
      target.closest('[class*="side-buttons"]') ||
      // 时间控制器
      target.closest('[class*="TimeController"]') ||
      target.closest('[class*="time-controller"]') ||
      // Header区域
      target.closest('header') ||
      target.closest('[class*="Header"]');
    
    // 🌟 延迟执行，确保Cesium的事件处理完成后再处理关闭逻辑
    setTimeout(() => {
      // 🌟 重要：如果卫星点击正在处理中，不要关闭面板（最高优先级检查）
      if (isSatelliteClickInProgress) {
        console.log('🚫 卫星点击正在处理中，跳过关闭操作');
        return;
      }
      
      // 🌟 如果卫星基础信息面板正在加载，不要关闭面板
      if (satelliteBasicInfoLoading) {
        console.log('🚫 卫星信息正在加载中，跳过关闭操作');
        return;
      }
      
      // 🌟 如果点击在Cesium canvas上，但只在实际处理卫星点击时才跳过关闭
      // 移除这个检查，因为它过于宽泛，导致所有canvas点击都被阻止
      // if (isCesiumCanvasClick) {
      //   console.log('🎯 检测到Cesium canvas点击，可能是卫星点击，跳过关闭操作');
      //   return;
      // }
      
      // 如果点击的不是受保护区域，且有信息面板打开，则关闭信息面板
      if (!isClickInsideProtectedArea) {
        const hasOpenInfoPanels = selectedSatellite || 
                                 selectedDebris || 
                                 selectedLaunchSite || 
                                 launchSiteWikiVisible || 
                                 satelliteBasicInfoVisible;
        
        if (hasOpenInfoPanels) {
          console.log('🔄 点击空白区域，关闭所有信息面板');
          handleCloseAllInfoPanels();
        }
      }
    }, 200); // 减少延迟时间到200ms，提升响应速度
  }, [
    selectedSatellite,
    selectedDebris,
    selectedLaunchSite,
    launchSiteWikiVisible,
    satelliteBasicInfoVisible,
    satelliteBasicInfoLoading, // 添加loading状态依赖
    isSatelliteClickInProgress, // 🌟 添加卫星点击处理标志依赖
    handleCloseAllInfoPanels,
    cesiumController
  ]);

  // 🌟 添加键盘和点击事件监听器（仅在CesiumController初始化后）
  useEffect(() => {
    // 只有在CesiumController完全初始化后才设置事件监听器
    if (!cesiumController) {
      return;
    }
    
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('click', handleDocumentClick);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('click', handleDocumentClick);
    };
  }, [handleKeyDown, handleDocumentClick, cesiumController]);

  useEffect(() => {
    const initController = async () => {
      if (containerRef.current && !cesiumController) {
        try {
          console.log('开始加载服务...');

          // 确保星座数据管理器已初始化（已在应用启动时初始化）
          console.log('🌟 确认星座数据管理器状态...');
          // constellationDataManager.init() 已在 main.tsx 中调用，此处不重复初始化
          console.log('✅ 星座数据管理器已就绪');

          // 动态导入所需服务
          const [
            { mockSatellites },
            { mockDebrisData },
            { launchSiteService: launchSiteServiceImpl },
            { groundStationService: groundStationServiceImpl }
          ] = await Promise.all([
            import('../../services/mockSatelliteData'),
            import('../../services/mockDebrisData'),
            import('../../services/mockLaunchSiteData'),
            import('../../services/mockGroundStationData')
          ]);

          // 验证服务是否正确加载
          if (!mockSatellites) {
            throw new Error('卫星服务加载失败');
          }
          if (!mockDebrisData) {
            throw new Error('碎片服务加载失败');
          }
          if (!launchSiteServiceImpl) {
            throw new Error('发射场服务加载失败');
          }
          if (!groundStationServiceImpl) {
            throw new Error('地面站服务加载失败');
          }

          console.log('所有服务加载完成，开始初始化CesiumController...');
          
          // 创建服务包装对象
          const satelliteService = {
            getAllSatellites: () => mockSatellites,
            getSatellitesByConstellation: (constellation: string) => 
              mockSatellites.filter(sat => sat.constellationName === constellation),
            getSatelliteById: (id: string) =>
              mockSatellites.find(sat => sat.id === id)
          };

          const debrisService = {
            getAllDebris: () => Promise.resolve(mockDebrisData),
            getDebrisById: (id: string) => 
              Promise.resolve(mockDebrisData.find(debris => debris.id === id))
          };

          const controller = new CesiumController(
            containerRef.current,
            satelliteService,
            debrisService,
            launchSiteServiceImpl,
            groundStationServiceImpl
          );

          // 设置事件处理器
          if (handleSatelliteClick) {
            controller.setOnSatelliteClick((satellite: Satellite) => {
              handleSatelliteClick(satellite);
            });
          }

          controller.setOnDebrisClick((debris) => {
            setSelectedDebris(debris);
          });

          // 设置发射场点击回调
          controller.setOnLaunchSiteClick(handleLaunchSiteClick);

          // 🌟 新增：设置卫星信息请求回调
          controller.satelliteController.setOnSatelliteInfoRequest(handleSatelliteInfoRequest);

          setCesiumController(controller);
          console.log('CesiumController初始化完成');

        } catch (error) {
          console.error('初始化失败:', error);
          message.error(`初始化失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
      }
    };

    initController();
  }, [containerRef, cesiumController, handleSatelliteClick, handleSatelliteInfoRequest, handleLaunchSiteClick]);

  // 通过NORAD ID处理卫星显示的函数
  const handleSatelliteByNoradId = useCallback(async (noradIdStr: string) => {
    if (!cesiumController) {
      console.error('CesiumController not initialized');
      return;
    }

    try {
      const noradIdNum = parseInt(noradIdStr, 10);
      if (isNaN(noradIdNum) || noradIdNum <= 0) {
        console.error('Invalid NORAD ID:', noradIdStr);
        message.error('无效的NORAD ID');
        return;
      }

      console.log(`🔍 开始通过NORAD ID搜索卫星: ${noradIdNum}`);

      const { tleDataManager } = await import('../../services/tleDataManager');
      
      const allTleData = await tleDataManager.getTleData();

      if (allTleData.length === 0) {
        console.error('未获取到TLE数据');
        message.error('未获取到TLE数据，请检查网络连接');
        return;
      }

      const foundSatellite = allTleData.find(tle => tle.norad_id === noradIdNum);

      if (!foundSatellite) {
        console.warn(`未找到NORAD ID为 ${noradIdNum} 的卫星`);
        message.warning(`未找到NORAD ID为 ${noradIdNum} 的卫星`);
        return;
      }

      console.log(`✅ 找到卫星:`, foundSatellite);

      const tleLines = foundSatellite.tle_raw.split('\n');
      let line1 = '', line2 = '';

      for (const line of tleLines) {
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('1 ')) {
          line1 = trimmedLine;
        } else if (trimmedLine.startsWith('2 ')) {
          line2 = trimmedLine;
        }
      }

      if (!line1 || !line2) {
        console.error('TLE数据格式错误，无法解析');
        message.error('TLE数据格式错误，无法解析');
        return;
      }

      const satelliteData = [{
        id: foundSatellite.norad_id.toString(),
        name: foundSatellite.satellite_name || `Satellite-${foundSatellite.norad_id}`,
        line1,
        line2,
        constellation: 'Single-Satellite'
      }];

      console.log(`🚀 开始显示单个卫星:`, satelliteData[0]);

      cesiumController.hideAllSatellites();

      await cesiumController.hybridRenderSatellites(satelliteData);

      // 🌟 新增：显示卫星后自动触发跟踪功能，实现与单击卫星相同的效果
      console.log(`🎯 自动跟踪显示的卫星: ${satelliteData[0].id}`);
      
      // 等待一小段时间确保卫星完全加载
      setTimeout(async () => {
        try {
          const trackingSuccess = await cesiumController.satelliteController.trackSatelliteById(satelliteData[0].id);
          if (trackingSuccess) {
            console.log(`✅ 成功自动跟踪卫星: ${satelliteData[0].name}`);
            message.success(`成功显示并跟踪卫星: ${satelliteData[0].name} (NORAD ID: ${noradIdNum})`);
          } else {
            console.warn(`⚠️ 卫星显示成功但跟踪失败: ${satelliteData[0].name}`);
            message.success(`成功显示卫星: ${satelliteData[0].name} (NORAD ID: ${noradIdNum})`);
          }
        } catch (trackingError) {
          console.error('自动跟踪卫星失败:', trackingError);
          message.success(`成功显示卫星: ${satelliteData[0].name} (NORAD ID: ${noradIdNum})`);
        }
      }, 1000); // 等待1秒确保卫星完全加载

    } catch (error) {
      console.error('通过NORAD ID搜索卫星失败:', error);
      message.error('搜索卫星失败: ' + (error instanceof Error ? error.message : String(error)));
    }
  }, [cesiumController]);

  useEffect(() => {
    if (!cesiumController) {
      console.log('CesiumController not initialized yet');
      return;
    }

    const type = searchParams.get('type');
    const id = searchParams.get('id');
    const tle1 = searchParams.get('tle1');
    const tle2 = searchParams.get('tle2');
    const noradId = searchParams.get('noradId');
    const longitude = searchParams.get('longitude');
    const latitude = searchParams.get('latitude');
    const altitude = searchParams.get('altitude');

    console.log('URL Parameters:', { type, id, tle1, tle2, noradId, longitude, latitude, altitude });

    if (!type || !id) {
      console.log('Missing required parameters: type or id');
      return;
    }

    if (['satellite', 'debris', 'spacestation'].includes(type)) {
      if (noradId) {
        console.log('Processing NORAD ID parameter:', noradId);
        handleSatelliteByNoradId(noradId);
        return;
      }
      
      if (!tle1 || !tle2) {
        console.log('Missing TLE data for space object');
        return;
      }
      console.log('Locating space object:', { type, id, tle1, tle2 });
      cesiumController.locateSpaceObject(id, type, { tle1, tle2 }).catch(error => {
        console.error('Error locating space object:', error);
      });
    }
    else if (['launchsite', 'groundstation'].includes(type)) {
      if (!longitude || !latitude || !altitude) {
        console.log('Missing position data for ground object');
        return;
      }
      const position = {
        longitude: parseFloat(longitude),
        latitude: parseFloat(latitude),
        altitude: parseFloat(altitude)
      };
      console.log('Locating ground object:', { type, id, position });
      cesiumController.locateGroundObject({ id, type, position }).catch(error => {
        console.error('Error locating ground object:', error);
      });
    } else {
      console.log('Unknown object type:', type);
    }
  }, [cesiumController, searchParams, handleSatelliteByNoradId]);

  const handleViewSatelliteDetails = useCallback(() => {
    // 处理查看卫星详情的逻辑
    if (selectedSatellite) {
      console.log('View satellite details:', selectedSatellite);
    }
  }, [selectedSatellite]);

  // 为图层面板提供控制器
  const getCesiumControllerForLayersPanel = () => {
    return cesiumController || undefined;
  };

  return (
    <PageContainer>
      <Header />
      <MainContent>
        <div ref={containerRef} className="cesium-container" />
        <SideButtons
          onLayersClick={handleLayersClick}
          layersPanelVisible={layersPanelVisible}
        />
        {cesiumController && (
          <TimeController
            currentTime={new Date(currentTime)}
            multiplier={multiplier}
            isPlaying={isPlaying}
            isDisabled={isTimelineDisabled}
            onMultiplierChange={handleMultiplierChange}
            onPlayPauseToggle={handlePlayPauseToggle}
            onRealTimeClick={handleRealTimeClick}
          />
        )}
        {/* 图层面板 */}
        {layersPanelVisible && (
          <LayersPanel 
            visible={layersPanelVisible} 
            cesiumController={getCesiumControllerForLayersPanel()}
          />
        )}
        {selectedSatellite && (
          <SatelliteInfoPanel
            satellite={selectedSatellite}
            onClose={handleClearSelectedSatellite}
            onViewDetails={handleViewSatelliteDetails}
          />
        )}
        {selectedDebris && (
          <DebrisInfoPanel
            debris={selectedDebris}
            onClose={() => setSelectedDebris(null)}
          />
        )}
        {selectedLaunchSite && (
          <LaunchSiteInfoPanel
            launchSite={selectedLaunchSite}
            onClose={() => setSelectedLaunchSite(null)}
          />
        )}
        {/* 发射场Wiki信息面板 */}
        {launchSiteWikiVisible && launchSiteWikiInfo && (
          <LaunchSiteWikiPanel
            siteName={launchSiteWikiInfo.siteName}
            wikiInfo={launchSiteWikiInfo.wikiInfo}
            onClose={handleCloseLaunchSiteWiki}
          />
        )}
        {/* 🌟 新增：卫星基础信息面板 */}
        <SatelliteBasicInfoPanel
          satelliteData={satelliteBasicInfo}
          loading={satelliteBasicInfoLoading}
          error={satelliteBasicInfoError}
          visible={satelliteBasicInfoVisible}
          onClose={handleCloseSatelliteBasicInfo}
        />
      </MainContent>
    </PageContainer>
  );
} 