# 太空大数据平台 - 前端项目

## 项目概述
太空大数据平台是一个专业的航天信息平台，提供发射任务、新闻、分析报告等功能。

## 核心功能
1. **首页搜索** - 天基目标搜索功能
2. **发射页面** - 查看发射任务列表和详情
3. **新闻页面** - 航天相关新闻资讯
4. **分析页面** - 数据分析和报告
5. **空间态势** - 空间目标监控
6. **智能推荐** - 基于发射计划和新闻的个性化推荐

## 技术栈
- **前端框架**: React 18 + TypeScript
- **路由**: React Router v6
- **状态管理**: React Hooks + Context
- **UI组件**: Tailwind CSS
- **动画**: Framer Motion
- **HTTP客户端**: Axios
- **地图引擎**: Cesium.js
- **3D渲染**: Three.js

## 项目结构
```
frontend/src/
├── components/     # 公共组件
│   ├── common/     # 通用组件
│   ├── search/     # 搜索相关组件
│   ├── layout/     # 布局组件
│   └── ...
├── pages/          # 页面组件
│   ├── HomePage/   # 首页
│   ├── LaunchPage/ # 发射页面
│   ├── NewsPage/   # 新闻页面
│   └── ...
├── hooks/          # 自定义Hook
├── services/       # API服务
├── utils/          # 工具函数
├── types/          # 类型定义
└── router/         # 路由配置
```

## 当前已知问题及修复方案

### 1. 发射任务推荐点击404错误
**问题**: 首页推荐的发射任务点击后跳转到`/launch/:id`，但路由配置中缺少发射详情页面路由
**解决方案**: 
- 添加发射详情页面路由 `/launch/:id`
- 或修改推荐URL生成逻辑，统一跳转到发射列表页面

### 2. 推荐功能正常工作
**状态**: 推荐功能已正常实现，包括：
- 发射任务推荐（已发射 + 即将发射）
- 新闻推荐（最新3条新闻）
- 自动轮播和手动切换
- 智能分类标签

## 路由配置
- `/` - 首页
- `/launches` - 发射列表页面
- `/news` - 新闻列表页面
- `/news/:id` - 新闻详情页面
- `/analysis` - 分析页面
- `/space-situation` - 空间态势页面
- 其他详情页面路由...

## 使用说明
1. 首页提供天基目标搜索功能
2. 首页下方显示智能推荐，包括发射任务和新闻
3. 点击推荐项可跳转到相应详情页面
4. 推荐内容每3秒自动切换，支持手动切换

## 开发环境
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

## API服务
项目依赖后端API服务，主要接口包括：
- `/api/es/launch/search` - 发射任务搜索
- `/api/news/list` - 新闻列表
- 其他业务接口...

## 认证系统
项目已实现完善的认证系统，包括：
- 防循环刷新机制
- 智能重定向
- 登录状态管理
- 权限控制

## 最近更新
- 实现了智能推荐功能
- 修复了认证系统循环刷新问题
- 添加了回到顶部功能
- 优化了用户体验
- 修复了火箭名称中英文映射问题
- 大幅改进了实体识别逻辑：
  * 严格的语法检查，只识别真正的名词实体
  * 排除冠词、介词、连词、序数词等语法词汇
  * 智能上下文分析，避免误识别形容词用法
- 添加了新闻详情页原文链接功能
- **用户注册安全性提升**：
  * 添加了密码确认功能，用户需要输入两次密码
  * 实现了两次密码一致性验证，防止用户输入错误
  * 增加了密码长度要求（至少6位）
  * 优化了错误提示信息，提升用户体验
- **军事新闻筛选优化**：
  * 军事新闻tab现在同时包含索引模式和主题词筛选
  * 除了原有的"*defence*"和"*defense*"索引外，还会显示主题词包含"军事"的新闻
  * 支持用户标签筛选与军事主题的组合过滤
  * 提升了军事新闻内容的覆盖度和准确性

## 🚀 项目概述
基于React + TypeScript + Cesium的太空态势感知系统，提供卫星、碎片和空间目标的实时3D可视化功能。

## ✨ 核心特性

### 高性能渲染系统
- **混合渲染架构**: 3D Tiles + 快速渲染智能切换
- **3D Tiles模式**: 预计算点云数据，支持10万+卫星
- **快速渲染模式**: PointPrimitive高性能渲染，可流畅显示5000+卫星
- **Entity模式** (≤500对象): 完整功能，丰富交互
- **智能降级**: 自动检测数据可用性，无缝切换

### 卫星态势展示
- 多星座支持：Starlink、OneWeb、GPS、北斗等
- 实时轨道计算基于TLE数据
- 颜色编码区分不同星座
- 智能LOD和距离控制
- **卫星详情页态势跳转**: 从卫星详情页点击态势按钮，可直接在态势页面显示该卫星
  - 自动提取卫星NORAD ID
  - 使用TLE数据管理器查找卫星轨道数据
  - 在3D地球上精确定位和显示卫星
  - **自动跟踪功能**: 显示卫星后自动触发跟踪效果，与单击卫星效果一致
    - 相机自动跟踪卫星运动
    - 显示卫星详细信息面板
    - 创建高亮的跟踪实体（卫星、轨道、标签）
    - 隐藏原始点卫星，突出显示跟踪目标
  - 支持备用TLE数据传递方式

### 🕐 数据自动刷新系统
- **应用启动即开始**: 打开项目后立即开始数据刷新任务，无需等待进入态势页面
- **TLE数据管理**: 
  - 每4小时自动从后端API获取最新TLE数据
  - 首次启动时自动检查本地数据，如无数据或过期则立即获取
  - 使用IndexedDB本地缓存，减少网络请求
  - 智能重试机制，网络失败时自动重试
- **星座数据管理**:
  - 每12小时自动刷新星座信息和卫星映射关系
  - 首次启动时自动检查本地数据，确保数据可用性
  - 支持大量星座的批量处理和更新
- **状态监控**: 实时显示数据状态、更新进度和错误信息
- **手动刷新**: 支持用户主动刷新数据

## 🛠️ 技术架构

### 核心组件

#### PointPrimitiveManager
高性能点渲染管理器，负责大量卫星的批量渲染：

```typescript
export class PointPrimitiveManager {
  addSatellite(id: string, name: string, position: SampledPositionProperty, color: Color): void
  addDebris(id: string, name: string, position: SampledPositionProperty, color: Color): void
  setLabelsVisible(visible: boolean): void
  getSatelliteCount(): number
}
```

#### HybridSatelliteRenderer
混合卫星渲染器，智能选择最佳渲染方案：

```typescript
export class HybridSatelliteRenderer {
  // 自动选择最佳渲染模式
  async renderSatellites(data?: SatelliteData[]): Promise<void>
  
  // 手动切换渲染模式
  async switchRenderMode(mode: 'tiles' | 'fast' | 'auto'): Promise<void>
  
  // 获取时间控制器
  getTimeController(): SatelliteTimeController
}
```

#### CesiumController  
主要的3D场景控制器，集成混合渲染器：

```typescript
// 使用混合渲染器（3D Tiles优先，自动降级）
async hybridRenderSatellites(tleDataList?: Array<TleData>): Promise<void>

// 传统快速渲染方法
async fastRenderAllSatellites(tleDataList: Array<TleData>): Promise<void>
```

#### 数据管理器

##### TleDataManager
TLE数据管理服务，负责自动获取和缓存卫星轨道数据：

```typescript
export class TleDataManager {
  // 初始化管理器，启动定时更新（在main.tsx中调用）
  async init(): Promise<void>
  
  // 获取TLE数据（优先使用缓存）
  async getTleData(): Promise<TleDataItem[]>
  
  // 手动刷新数据
  async refreshData(): Promise<TleDataCache | null>
  
  // 获取数据状态
  getDataStatus(): TleDataStatus
}
```

##### ConstellationDataManager
星座数据管理服务，负责星座信息和卫星映射关系：

```typescript
export class ConstellationDataManager {
  // 初始化管理器，启动定时更新（在main.tsx中调用）
  async init(): Promise<void>
  
  // 获取星座列表
  async getConstellationList(): Promise<ConstellationInfo[]>
  
  // 获取指定星座的卫星ID列表
  async getConstellationSatelliteIds(name: string): Promise<number[]>
  
  // 获取数据状态
  getDataStatus(): ConstellationDataStatus
}
```

##### 应用启动时的初始化流程（main.tsx）
```typescript
// 应用启动时立即初始化所有数据管理器
tleDataManager.init().catch(error => {
  console.error('TLE数据管理器初始化失败:', error);
});

constellationDataManager.init().catch(error => {
  console.error('星座数据管理器初始化失败:', error);
});
```

## 📈 性能优化

### PointPrimitive模式优势
- **渲染性能**: 提升10-50倍
- **内存占用**: 减少90%
- **GPU效率**: 批量渲染，单一draw call
- **响应速度**: 毫秒级交互响应

### 优化策略
1. 智能模式选择（500个对象为分界点）
2. 异步分批处理
3. 距离显示条件
4. 智能缓存机制

## 🎯 使用指南

### 基本操作
1. 加载卫星：通过图层面板选择显示目标
2. 时间控制：使用时间轴控制播放速度
3. 导航：鼠标拖拽旋转，滚轮缩放
4. 选择：点击卫星查看详细信息

### 性能建议
- 大量卫星时关闭标签显示
- 按需开启轨道显示
- 时间倍速时减少显示对象

### 调试功能
```javascript
// 控制台调试接口
window.debugPointPrimitives.getSatelliteCount()
window.debugPointPrimitives.setLabelsVisible(true)

// IndexedDB诊断和修复工具
window.indexedDBDiagnostic.diagnose()           // 诊断数据库问题
window.indexedDBDiagnostic.resetDatabase()      // 重置数据库
window.indexedDBDiagnostic.clearAllSpaceData()  // 清理所有太空数据
window.indexedDBDiagnostic.checkHealth()        // 健康检查

// 多层缓存管理工具
window.multilayerCacheManager.getTleData()           // 获取缓存数据
window.multilayerCacheManager.clearAllCache()        // 清理所有缓存
window.multilayerCacheManager.clearLocalStorageOnly() // 仅清理localStorage缓存
window.multilayerCacheManager.getStorageStatus()     // 查看存储状态
```

## 🚀 开发环境

```bash
# 安装依赖
npm install

# 启动开发服务器  
npm start

# 构建生产版本
npm run build
```

## 📊 性能指标
- **渲染速度**: 快速模式3-10秒完成（原来2-5分钟）
- **帧率**: 目标60FPS，PointPrimitive模式下稳定50+FPS
- **内存**: PointPrimitive模式减少90%占用
- **CPU使用率**: 快速渲染模式减少80%
- **交互响应**: <100ms

## 更新日志

### v3.2.3 (当前) - 基于角色的权限控制系统
- 🔐 **重要功能**：实现完整的基于角色的访问控制(RBAC)系统
- 🚫 **导航权限控制**：
  - "深度分析"导航项只对管理员用户可见
  - "关注目标"导航项只对管理员用户可见
  - 普通用户无法看到这些敏感功能的入口
- 👤 **用户菜单权限控制**：
  - "关注目标管理"只对管理员用户可见
  - "关键词订阅"只对管理员用户可见
  - "会员权限管理"只对管理员用户可见
  - 普通用户只能看到"个人信息"菜单项
- 🛠️ **技术实现**：
  - 在NavItemType接口中新增requiredRole字段支持权限配置
  - 导航组件根据用户角色动态过滤显示项目
  - 用户菜单组件根据权限智能隐藏管理功能
  - 统一使用UserRole.ADMIN和UserRole.FREE角色枚举
- 🔄 **向后兼容**：现有功能保持完全兼容，无权限要求的功能对所有用户可见
- 🎯 **安全增强**：从前端UI层面确保敏感功能入口的访问控制
- 📱 **用户体验**：权限过滤实时生效，无需刷新页面
- 🧩 **架构优化**：统一使用Navigation和UserMenu组件，避免硬编码导航
- ⚡ **性能优化**：权限检查高效执行，对性能无明显影响

### v3.2.2 - 防循环刷新认证系统
- 🛡️ **重要改进**：实现完善的防循环刷新认证系统，彻底解决登录页面循环刷新问题
- 🔧 **API拦截器优化**：
  - 使用AuthUtils工具类替代原始的防循环逻辑
  - 实现5秒冷却时间和最大3次连续跳转限制
  - 优化401错误处理，避免强制页面刷新
  - 智能跳转状态管理，自动重置计数器
- 🎯 **AuthGuard增强**：
  - 添加详细的认证检查流程日志
  - 实现防重复检查机制（1秒内最多检查一次）
  - 优化加载状态显示，提供更好的用户体验
  - 增强错误处理和状态同步
- 🚀 **AuthUtils工具类**：
  - 统一管理所有认证相关逻辑
  - 提供完整的防循环检查机制
  - 智能重定向路径管理
  - 支持开发环境调试接口
- 🧪 **测试工具集**：
  - 创建AuthTestUtils测试工具类
  - 支持循环跳转、状态重置、路径管理等全方位测试
  - 提供并发测试能力，验证高负载下的稳定性
  - 开发环境下可通过`window.authTestUtils.runAllTests()`运行完整测试
- 📱 **登录页面优化**：
  - 使用AuthUtils统一管理重定向逻辑
  - 登录成功后自动清理临时状态
  - 支持多种来源的重定向地址
  - 改进错误处理和用户反馈
- 🔍 **调试功能**：
  - 开发环境下提供`window.authUtils`调试接口
  - 详细的控制台日志记录认证流程
  - 实时查看跳转状态和冷却时间
  - 支持手动重置和状态检查
- ⚡ **性能优化**：
  - 减少不必要的状态检查和API调用
  - 优化内存使用，及时清理临时数据
  - 智能缓存机制，避免重复验证
- 🛠️ **代码质量**：
  - 完整的TypeScript类型定义
  - 详细的JSDoc注释
  - 模块化设计，易于维护和扩展
  - 遵循SOLID原则

### v3.2.1 - API路径修复
- 🐛 **重要修复**：修复星座名称API路径错误问题
- 🔧 **问题解决**：将`constellationService.getConstellationNames()`方法中的错误路径`/search/constellation/names`修正为`/constellation/names`
- 📡 **API统一**：确保所有星座名称相关API调用使用正确的后端接口路径
- ⚡ **性能提升**：修复后前端不再出现404错误，提升用户体验
- 🛠️ **代码质量**：统一API路径规范，避免类似问题再次发生
- 📋 **影响范围**：修复影响搜索建议、星座筛选等功能的API调用问题

### v3.2.0 - 卫星态势跟踪功能增强
- 🎯 **重要功能**：实现从卫星详情页跳转到态势页面时的自动跟踪功能
- 🚀 **自动跟踪**：显示卫星后自动触发跟踪效果，与在态势页面单击卫星效果完全一致
- 📹 **相机跟踪**：相机自动跟踪卫星运动，提供最佳观察视角
- 📊 **信息面板**：自动显示卫星详细信息面板，包含轨道参数、基本信息等
- 🎨 **视觉增强**：创建高亮的跟踪实体（卫星、轨道、标签），突出显示跟踪目标
- 🔧 **API增强**：在SatelliteController中新增`trackSatelliteById`公开方法
- ⚡ **智能延迟**：等待卫星完全加载后再触发跟踪，确保功能稳定性
- 🛠️ **错误处理**：完善的错误处理机制，跟踪失败时仍能正常显示卫星
- 📱 **用户体验**：从卫星详情页到态势页面的无缝衔接，提升操作流畅性
- 🔍 **调试优化**：详细的控制台日志，便于跟踪功能执行过程

### v3.1.9 - 发射场详情页功能增强
- 🚀 **新增功能**：发射场详情页增加发射信息tab，展示该发射场的所有发射记录
- 📊 **发射统计**：简化统计信息显示，仅显示总发射次数
- 🎯 **API集成**：新增`searchLaunchInfo`方法，调用`/api/es/launch/search`接口获取发射数据
- 📋 **信息展示**：参考发射页面样式，展示发射日期、火箭名称、任务名称、状态等关键信息
- 🔄 **自动加载**：实现滚动自动加载功能，用户滚动到底部时自动加载更多数据
- 🎨 **界面优化**：去除发射场信息面板和详情页的信息来源部分，界面更简洁
- 📱 **响应式设计**：发射信息卡片支持响应式布局，适配不同屏幕尺寸
- 🏷️ **状态标识**：清晰的发射状态标识（成功、失败、即将发射等）
- 💾 **类型安全**：新增LaunchInfo、LaunchInfoSearchRequest等TypeScript接口
- ⚡ **性能优化**：智能滚动加载和状态管理，提升用户体验

### v3.1.8 - 发射场信息关联优化
- 🎯 **重要功能**：实现发射场_id字段关联，解决发射场信息无法正确显示的问题
- 🔗 **数据关联**：发射场图层API返回的_id字段现在用于精确获取详细信息
- 📍 **智能查询**：点击发射场图标时优先使用_id查询，失败时自动回退到名称查询
- 🛠️ **API增强**：新增`getLaunchSiteWikiInfoById`方法，支持通过_id获取发射场Wiki信息
- 🔄 **双重保障**：确保即使_id查询失败，仍可通过名称获取发射场信息
- 📄 **详情页优化**：发射场详情页支持URL参数传递_id，提高信息获取准确性
- 🎨 **界面改进**：信息面板"查看详情"按钮现在会传递_id参数到详情页
- 📊 **类型安全**：更新TypeScript接口，增加_id字段支持
- 🔍 **调试增强**：添加详细的控制台日志，便于跟踪数据获取过程
- ✅ **兼容性**：保持向后兼容，不影响现有功能的正常使用

### v3.1.7 (当前) - 多层缓存系统优化与错误处理增强
- 🛠️ **重要修复**：解决localStorage配额满时导致TLE数据刷新失败的问题
- 🧹 **自动清理**：localStorage配额满时自动清理旧数据并重试保存
- 🔄 **容错机制**：即使所有存储层保存失败，系统也不会中断运行
- 📊 **详细日志**：增强错误处理和状态报告，便于问题诊断
- 🧰 **新增工具**：`window.multilayerCacheManager.clearLocalStorageOnly()`手动清理localStorage
- 🛡️ **稳定性提升**：多层缓存系统现在更加稳定可靠，确保TLE数据始终可用

### v3.1.6 - 多层缓存系统重构
- 🚀 **重大升级**：实现多层缓存系统，彻底解决IndexedDB不稳定问题
- 🗂️ **缓存层级**：内存缓存 → localStorage → sessionStorage → IndexedDB，自动降级
- 📦 **数据压缩**：使用pako库对localStorage数据进行gzip压缩，节省70-80%空间
- 📄 **分块存储**：大数据自动分块存储到localStorage，突破5MB限制
- 🛡️ **故障恢复**：即使IndexedDB完全不可用，系统依然能正常缓存数据
- ⚡ **性能优化**：内存缓存提供毫秒级访问，localStorage提供持久化备份
- 🔧 **智能管理**：自动选择最佳可用存储层，透明处理存储失败
- 📊 **状态监控**：实时监控所有存储层状态和可用性
- 🧰 **调试工具**：`window.multilayerCacheManager`提供完整的缓存管理接口
- 🌟 **用户体验**：无论浏览器存储环境如何，都能确保TLE数据缓存功能

### v3.1.5 - IndexedDB缓存系统增强与故障恢复
- 🛠️ **重要修复**：解决TLE数据手动更新时IndexedDB初始化超时问题
- ⏰ **超时优化**：初始化超时从15秒延长至30秒，支持多次重试机制
- 🔄 **自动恢复**：数据库初始化失败时自动重置并重新创建
- 🏥 **健康检查**：新增数据库健康检查机制，预防性检测连接问题
- 🔧 **错误处理**：增强错误分类和针对性解决方案提示
- 💾 **存储优化**：改进大数据量（9MB+）存储性能，增加事务超时保护
- 🛡️ **重试机制**：保存操作支持最多3次重试，指数退避策略
- 🩺 **诊断工具**：新增IndexedDB诊断工具，支持手动修复
- 📊 **状态监控**：实时监控数据库连接状态和存储配额
- 🧰 **开发者工具**：控制台可用`window.indexedDBDiagnostic`进行故障排查

### v3.1.4 - 发射页面滚动加载优化 & 回到顶部功能
- ✨ **用户体验提升**：发射页面列表改为滚动自动加载，无需点击"加载更多"按钮
- 🔄 **无限滚动**：当用户滚动到页面底部时自动加载下一页数据
- ⚡ **智能触发**：提前100px开始加载，确保流畅的浏览体验
- 🎯 **精确控制**：使用Intersection Observer API实现高性能滚动检测
- 🛠️ **技术实现**：新增useInfiniteScroll自定义Hook，可复用于其他页面
- 📱 **移动友好**：优化移动设备上的滚动加载体验
- 🔝 **回到顶部功能**：为所有滚动页面添加回到顶部按钮，支持平滑滚动
- 🎨 **统一体验**：回到顶部按钮在发射页面、新闻页面、搜索页面、分析页面、关注目标页面、火箭页面、发射场详情页面、管理页面、设置页面等统一显示
- 🔧 **智能适配**：自动检测滚动容器，支持MainContentWrapper和普通window滚动

### v3.1.3 - 卫星跟踪平滑过渡功能
- ✨ **新功能**：实现卫星跟踪切换时的平滑相机过渡效果
- 🎬 **用户体验**：从一颗卫星切换到另一颗卫星时，相机会平滑飞行而不是突然跳转
- ⚡ **智能检测**：自动检测是否为跟踪切换场景，首次跟踪保持原有快速响应
- 🔧 **技术实现**：使用camera.flyTo()实现2.5秒平滑过渡，支持状态管理和错误恢复
- 🛡️ **防冲突机制**：过渡期间禁用新的跟踪请求，避免操作冲突
- 🧪 **测试支持**：新增testSmoothTransition()方法验证平滑过渡效果

### v3.1.2 - 卫星跟踪白色点闪烁修复
- 🐛 **重要修复**：解决卫星跟踪时白色原始点与青色跟踪点同时显示的问题
- 🔧 **技术方案**：在LightSatelliteRenderer中添加hiddenSatellites跟踪机制
- ✨ **功能改进**：updatePositionsRealtime方法现在尊重隐藏状态，不会强制显示被隐藏的卫星
- 🧪 **调试支持**：新增getHiddenSatellites()、isHidden()等调试方法
- 📝 **详细日志**：增强跟踪过程的调试信息，便于问题排查

### v3.1.1 - 地球自转方向修复
- 🐛 **重要修复**：修正地球自转方向，从错误的自东向西转改为正确的自西向东转
- ✨ **物理准确性**：地球自转现在符合真实物理规律
- 🎯 **视觉一致性**：地球自转与卫星运动方向现在保持一致
- 📝 技术细节：移除了CesiumController中updateEarthRotation方法的错误负号

### v3.1.0 - TLE数据管理系统
- 🔄 **自动数据更新**：每4小时自动从API获取最新TLE数据
- 💾 **本地缓存系统**：使用IndexedDB存储，减少API请求
- ⚡ **性能提升**：全部卫星功能响应时间从几秒缩短到毫秒级
- 🔧 **智能重试机制**：网络失败时自动重试，指数退避策略
- 📊 **状态监控**：实时显示数据状态、更新进度和错误信息
- 🔄 **手动刷新**：支持用户主动刷新TLE数据
- 📝 API端点：`POST /orbit/bulk-tle/all?sampleMode=false`

### v2.1.0 - 快速渲染优化
- ⚡ **重大性能提升**：卫星渲染时间从几分钟缩短到几秒钟
- 🚀 新增fastRenderAllSatellites方法，一次性渲染所有卫星
- 📊 性能提升10-50倍，内存占用减少90%
- 🔧 简化位置计算，只计算当前时刻位置
- ✨ 并行计算所有卫星位置，批量渲染
- 📝 详细文档：FAST_RENDERING_OPTIMIZATION.md

### v3.0.0 (当前开发) - 3D Tiles混合渲染
- 🎯 **混合渲染架构**：3D Tiles + 快速渲染智能切换
- 🚀 **性能革命**：支持10万+卫星，渲染时间0.1-0.5秒
- 📦 **3D Tiles支持**：预计算点云数据，流式加载
- 🔄 **智能降级**：自动检测数据可用性，无缝切换渲染模式
- 🕐 **时间控制**：支持历史数据回放和实时更新
- 📝 详细方案：3D_TILES_SATELLITE_OPTIMIZATION.md

### v2.0.1
- 🐛 修复"全部卫星"图层取消选中时卫星不消失的问题
- ✨ 新增hideAllSatellites方法，支持双重渲染模式清理
- 🔧 优化图层控制逻辑，提升用户体验

### v2.0.0
- ✨ 新增PointPrimitive高性能模式
- 🚀 支持5000+卫星流畅显示
- 📈 性能提升10-50倍
- 🎯 智能渲染模式选择

## 🔧 技术要求
- Node.js 16+
- 支持WebGL 2.0的现代浏览器
- 8GB+内存（推荐16GB）

### API配置管理

项目使用统一的API配置系统，确保所有网络请求都使用可配置的API地址：

- **中心配置**: `frontend/src/services/api.ts` 中的 `API_BASE_URL`
- **自动配置脚本**: `fix-api-config.sh` 可一键修改API地址并重新构建
- **统一导入**: 所有组件都从 `../services/api` 导入 `API_BASE_URL`
- **避免硬编码**: 移除了所有直接使用 `localhost` 或固定IP的fetch调用

使用配置脚本修改API地址：
```bash
# 运行配置脚本，按提示输入新的API地址
./fix-api-config.sh
```

最近修复的API配置问题：
- ✅ `useLaunchContractors.ts` - 发射承包商数据API
- ✅ `useSatelliteSearch.ts` - 卫星搜索和筛选API  
- ✅ `SpaceTargetSearch.tsx` - 碎片搜索API
- ✅ 所有组件现在都使用可配置的 `API_BASE_URL`

---

**专业用途设计，处理大数据时请确保充足计算资源**

## 功能特性

### 🛰️ 卫星交互功能

#### 1. 鼠标悬停卫星
- **功能**: 鼠标悬停在卫星上时显示卫星信息
- **效果**: 
  - 显示黄色发光的卫星点
  - 显示黄色轨道线
  - 显示卫星名称标签
- **使用方法**: 将鼠标移动到卫星上，等待300毫秒后自动显示

#### 2. 🌟 单击卫星跟踪 (新功能)
- **功能**: 单击卫星后开始跟踪，相机会跟随卫星移动
- **效果**:
  - 显示青色发光的卫星点（比悬停效果更明显）
  - 显示黄色轨道线
  - 显示带跟踪图标的卫星名称标签
  - 相机自动跟随卫星运动
- **使用方法**:
  - 单击任意卫星开始跟踪
  - 再次单击同一卫星停止跟踪
  - 单击空白区域停止跟踪
  - 单击其他卫星切换跟踪目标

#### 3. 双击卫星详情
- **功能**: 双击卫星显示详细信息面板
- **效果**: 打开卫星详情侧边栏，显示完整的卫星信息

### 🔧 调试单击跟踪功能

如果单击卫星后没有切换到跟踪视角，请按以下步骤调试：

#### 1. 打开浏览器开发者工具
- 按F12或右键选择"检查"
- 切换到"Console"标签页

#### 2. 检查事件是否触发
单击卫星后，控制台应该显示类似以下日志：
```javascript
// 获取SatelliteController实例（假设在全局变量中）
const satelliteController = window.cesiumController?.satelliteController;

// 测试跟踪第一个可用的卫星
satelliteController?.testTrackFirstSatellite();

// 停止跟踪
satelliteController?.stopSatelliteTracking();

// 检查当前跟踪状态
console.log('当前跟踪的卫星ID:', satelliteController?.getTrackedSatelliteId());
console.log('是否正在跟踪:', satelliteController?.isTrackingSatellite());
```

#### 3. 手动测试跟踪功能
在控制台中输入以下命令进行测试：
```javascript
// 获取SatelliteController实例（假设在全局变量中）
const satelliteController = window.cesiumController?.satelliteController;

// 测试跟踪第一个可用的卫星
satelliteController?.testTrackFirstSatellite();

// 停止跟踪
satelliteController?.stopSatelliteTracking();

// 检查当前跟踪状态
console.log('当前跟踪的卫星ID:', satelliteController?.getTrackedSatelliteId());
console.log('是否正在跟踪:', satelliteController?.isTrackingSatellite());
```

#### 4. 🌟 最新修复 (已解决的问题)

**问题**: Entity id属性只读错误
- **错误信息**: "Cannot set property id of #<Entity> which has only a getter"
- **解决方案**: 移除所有Entity创建时的id设置，让Cesium自动生成唯一ID
- **状态**: ✅ 已修复

**问题**: 位置更新日志刷屏
- **现象**: 控制台不断输出"卫星位置向量异常"和"位置更新完成"日志
- **解决方案**: 注释掉LightSatelliteRenderer中的相关日志输出
- **状态**: ✅ 已修复

#### 5. 常见问题排查

**问题1: 没有看到单击事件日志**
- 可能原因：事件处理器冲突或未正确注册
- 解决方案：刷新页面重新加载

**问题2: 看到单击事件但没有跟踪效果**
- 可能原因：卫星数据获取失败或实体创建失败
- 解决方案：检查控制台错误信息

**问题3: 实体创建成功但相机不跟踪**
- 可能原因：viewer.trackedEntity设置失败
- 解决方案：查看相机跟踪验证日志

**问题4: 相机跟踪设置成功但视角没有变化**
- 可能原因：卫星位置计算错误或时间同步问题
- 解决方案：检查实体位置和时钟状态

### 🎮 交互说明

1. **悬停 vs 跟踪**:
   - 悬停：临时显示，鼠标移开后消失
   - 跟踪：持续显示，相机跟随，需要手动停止

2. **优先级**:
   - 跟踪状态下会禁用悬停功能
   - 双击事件优先级高于单击事件

3. **视觉区别**:
   - 悬停：黄色卫星点 + 黄色轨道
   - 跟踪：青色卫星点 + 黄色轨道 + 🎯图标标签

### 🔧 技术实现

- 使用Cesium的`trackedEntity`实现相机跟踪
- 复用悬停功能的实体创建逻辑
- 惯性坐标系确保轨道显示正确
- 事件处理避免单击和双击冲突

### 📱 使用建议

1. 先通过图层面板加载卫星数据
2. 使用鼠标悬停快速查看卫星信息
3. 单击感兴趣的卫星进行跟踪观察
4. 双击卫星查看详细技术参数